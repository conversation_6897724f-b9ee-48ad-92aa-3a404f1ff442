<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>活动管理系统接口设计文档 - 补充接口</title>
    <style>
        body {
            font-family: "宋体", SimSun, serif;
            font-size: 10.5pt;
            line-height: 1.5;
            margin: 0;
            padding: 20px;
            background-color: #fff;
        }
        
        .container {
            width: 800px;
            margin: 0 auto;
            background-color: #fff;
        }
        
        h1 {
            font-family: "宋体", SimSun, serif;
            font-size: 14pt;
            font-weight: bold;
            text-align: center;
            margin: 30px 0;
            color: #000;
        }
        
        .first_title {
            font-family: "宋体", SimSun, serif;
            font-size: 12pt;
            font-weight: bold;
            color: #000;
            margin: 20px 0 10px 0;
        }
        
        .second_title {
            font-family: "宋体", SimSun, serif;
            font-size: 10.5pt;
            font-weight: bold;
            color: #000;
            margin: 15px 0 8px 0;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            table-layout: fixed;
            border: 0.5px solid #000;
        }
        
        td {
            padding-left: 10px;
            border: 0.5px solid #000;
            height: auto;
            min-height: 32px;
            word-break: break-all;
            word-wrap: break-word;
            font-size: 10.5pt;
            vertical-align: top;
        }
        
        .bg {
            background-color: #D9D9D9;
            font-weight: bold;
            text-align: center;
        }
        
        .specialHeight {
            height: auto;
            min-height: 60px;
        }
        
        pre {
            font-family: "宋体", SimSun, serif;
            font-size: 9pt;
            margin: 5px 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        div {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>活动管理系统接口设计文档 - 补充接口</h1>
        
        <div style="margin-bottom:20px;">
            <h4 class="first_title">管理后台补充接口</h4>

            <div>
                <h4 class="second_title">删除活动类别接口</h4>
                <h5 class="second_title">功能点描述</h5>
                <div>管理后台删除活动类别，只有没有关联活动的类别才能删除。</div>

                <h5 class="second_title">输入</h5>
                <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                    <tr class="bg">
                        <td colspan="5">设计项</td>
                    </tr>
                    <tr>
                        <td width="25%">功能说明</td>
                        <td colspan="4">删除活动类别，只有没有关联活动的类别才能删除</td>
                    </tr>
                    <tr>
                        <td width="25%">接口提供者</td>
                        <td colspan="4">mini-app-admin</td>
                    </tr>
                    <tr>
                        <td width="25%">接口调用者</td>
                        <td colspan="4">管理后台前端</td>
                    </tr>
                    <tr>
                        <td width="25%">协议</td>
                        <td colspan="4">http POST</td>
                    </tr>
                    <tr>
                        <td width="25%">请求头</td>
                        <td colspan="4">Content-Type: application/json</td>
                    </tr>
                    <tr>
                        <td width="25%">请求路径</td>
                        <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/delete</td>
                    </tr>
                    <tr>
                        <td width="25%">超时时长</td>
                        <td colspan="4">5000ms</td>
                    </tr>
                    <tr>
                        <td width="25%">请求报文</td>
                        <td colspan="4">请求体： json字符串</td>
                    </tr>
                    <tr>
                        <td width="25%">响应报文</td>
                        <td colspan="4">响应体： json字符串</td>
                    </tr>
                </table>
                <br>
                <br>

                <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                    <tr class="bg">
                        <td width="26%">参数名</td>
                        <td width="15%">数据类型</td>
                        <td width="15%">参数类型</td>
                        <td width="15%">是否必填</td>
                        <td width="29%">说明</td>
                    </tr>
                    <tr>
                        <td align="left">1.id</td>
                        <td>Int</td>
                        <td>body</td>
                        <td>Y</td>
                        <td>主键ID</td>
                    </tr>
                    <tr class="specialHeight">
                        <td class="bg">示例</td>
                        <td colspan="4"><pre>{
  "id": 1
}</pre></td>
                    </tr>
                </table>
                <br>
                <br>

                <h5 class="second_title">输出</h5>
                <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                    <tr class="bg">
                        <td>返回属性名</td>
                        <td colspan="2">类型</td>
                        <td colspan="2">说明</td>
                    </tr>
                    <tr>
                        <td align="left">1.resultCode</td>
                        <td colspan="2">String</td>
                        <td colspan="2">业务响应码</td>
                    </tr>
                    <tr>
                        <td align="left">2.resultMsg</td>
                        <td colspan="2">String</td>
                        <td colspan="2">业务响应信息</td>
                    </tr>
                    <tr class="specialHeight">
                        <td class="bg">示例</td>
                        <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</pre></td>
                    </tr>
                </table>
                <br>
                <br>
            </div>

            <div>
                <h4 class="second_title">删除活动内容接口</h4>
                <h5 class="second_title">功能点描述</h5>
                <div>管理后台删除活动内容，只有未发布状态的活动才能删除。</div>

                <h5 class="second_title">输入</h5>
                <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                    <tr class="bg">
                        <td colspan="5">设计项</td>
                    </tr>
                    <tr>
                        <td width="25%">功能说明</td>
                        <td colspan="4">删除活动内容，只有未发布状态的活动才能删除</td>
                    </tr>
                    <tr>
                        <td width="25%">接口提供者</td>
                        <td colspan="4">mini-app-admin</td>
                    </tr>
                    <tr>
                        <td width="25%">接口调用者</td>
                        <td colspan="4">管理后台前端</td>
                    </tr>
                    <tr>
                        <td width="25%">协议</td>
                        <td colspan="4">http POST</td>
                    </tr>
                    <tr>
                        <td width="25%">请求头</td>
                        <td colspan="4">Content-Type: application/json</td>
                    </tr>
                    <tr>
                        <td width="25%">请求路径</td>
                        <td colspan="4">http://{ip}:{port}/mini-app-admin/activityContent/delete</td>
                    </tr>
                    <tr>
                        <td width="25%">超时时长</td>
                        <td colspan="4">5000ms</td>
                    </tr>
                    <tr>
                        <td width="25%">请求报文</td>
                        <td colspan="4">请求体： json字符串</td>
                    </tr>
                    <tr>
                        <td width="25%">响应报文</td>
                        <td colspan="4">响应体： json字符串</td>
                    </tr>
                </table>
                <br>
                <br>

                <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                    <tr class="bg">
                        <td width="26%">参数名</td>
                        <td width="15%">数据类型</td>
                        <td width="15%">参数类型</td>
                        <td width="15%">是否必填</td>
                        <td width="29%">说明</td>
                    </tr>
                    <tr>
                        <td align="left">1.id</td>
                        <td>Int</td>
                        <td>body</td>
                        <td>Y</td>
                        <td>主键ID</td>
                    </tr>
                    <tr class="specialHeight">
                        <td class="bg">示例</td>
                        <td colspan="4"><pre>{
  "id": 1
}</pre></td>
                    </tr>
                </table>
                <br>
                <br>

                <h5 class="second_title">输出</h5>
                <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                    <tr class="bg">
                        <td>返回属性名</td>
                        <td colspan="2">类型</td>
                        <td colspan="2">说明</td>
                    </tr>
                    <tr>
                        <td align="left">1.resultCode</td>
                        <td colspan="2">String</td>
                        <td colspan="2">业务响应码</td>
                    </tr>
                    <tr>
                        <td align="left">2.resultMsg</td>
                        <td colspan="2">String</td>
                        <td colspan="2">业务响应信息</td>
                    </tr>
                    <tr class="specialHeight">
                        <td class="bg">示例</td>
                        <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</pre></td>
                    </tr>
                </table>
                <br>
                <br>
            </div>
        </div>

        <div style="margin-bottom:20px;">
            <h4 class="first_title">小程序端补充接口</h4>

            <div>
                <h4 class="second_title">小程序取消报名接口</h4>
                <h5 class="second_title">功能点描述</h5>
                <div>小程序端用户取消报名。</div>

                <h5 class="second_title">输入</h5>
                <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                    <tr class="bg">
                        <td colspan="5">设计项</td>
                    </tr>
                    <tr>
                        <td width="25%">功能说明</td>
                        <td colspan="4">用户取消报名</td>
                    </tr>
                    <tr>
                        <td width="25%">接口提供者</td>
                        <td colspan="4">mini-app-api</td>
                    </tr>
                    <tr>
                        <td width="25%">接口调用者</td>
                        <td colspan="4">小程序前端</td>
                    </tr>
                    <tr>
                        <td width="25%">协议</td>
                        <td colspan="4">http POST</td>
                    </tr>
                    <tr>
                        <td width="25%">请求头</td>
                        <td colspan="4">Content-Type: application/json</td>
                    </tr>
                    <tr>
                        <td width="25%">请求路径</td>
                        <td colspan="4">http://{ip}:{port}/mini-app-api/registration/cancel</td>
                    </tr>
                    <tr>
                        <td width="25%">超时时长</td>
                        <td colspan="4">5000ms</td>
                    </tr>
                    <tr>
                        <td width="25%">请求报文</td>
                        <td colspan="4">请求体： json字符串</td>
                    </tr>
                    <tr>
                        <td width="25%">响应报文</td>
                        <td colspan="4">响应体： json字符串</td>
                    </tr>
                </table>
                <br>
                <br>

                <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                    <tr class="bg">
                        <td width="26%">参数名</td>
                        <td width="15%">数据类型</td>
                        <td width="15%">参数类型</td>
                        <td width="15%">是否必填</td>
                        <td width="29%">说明</td>
                    </tr>
                    <tr>
                        <td align="left">1.activityId</td>
                        <td>Int</td>
                        <td>body</td>
                        <td>Y</td>
                        <td>活动ID</td>
                    </tr>
                    <tr class="specialHeight">
                        <td class="bg">示例</td>
                        <td colspan="4"><pre>{
  "activityId": 1
}</pre></td>
                    </tr>
                </table>
                <br>
                <br>

                <h5 class="second_title">输出</h5>
                <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                    <tr class="bg">
                        <td>返回属性名</td>
                        <td colspan="2">类型</td>
                        <td colspan="2">说明</td>
                    </tr>
                    <tr>
                        <td align="left">1.resultCode</td>
                        <td colspan="2">String</td>
                        <td colspan="2">业务响应码</td>
                    </tr>
                    <tr>
                        <td align="left">2.resultMsg</td>
                        <td colspan="2">String</td>
                        <td colspan="2">业务响应信息</td>
                    </tr>
                    <tr class="specialHeight">
                        <td class="bg">示例</td>
                        <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "取消报名成功"
}</pre></td>
                    </tr>
                </table>
                <br>
                <br>
            </div>

            <div>
                <h4 class="second_title">小程序查询我的报名接口</h4>
                <h5 class="second_title">功能点描述</h5>
                <div>小程序端分页查询当前用户的报名记录。</div>

                <h5 class="second_title">输入</h5>
                <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                    <tr class="bg">
                        <td colspan="5">设计项</td>
                    </tr>
                    <tr>
                        <td width="25%">功能说明</td>
                        <td colspan="4">分页查询当前用户的报名记录</td>
                    </tr>
                    <tr>
                        <td width="25%">接口提供者</td>
                        <td colspan="4">mini-app-api</td>
                    </tr>
                    <tr>
                        <td width="25%">接口调用者</td>
                        <td colspan="4">小程序前端</td>
                    </tr>
                    <tr>
                        <td width="25%">协议</td>
                        <td colspan="4">http POST</td>
                    </tr>
                    <tr>
                        <td width="25%">请求头</td>
                        <td colspan="4">Content-Type: application/json</td>
                    </tr>
                    <tr>
                        <td width="25%">请求路径</td>
                        <td colspan="4">http://{ip}:{port}/mini-app-api/registration/myList</td>
                    </tr>
                    <tr>
                        <td width="25%">超时时长</td>
                        <td colspan="4">5000ms</td>
                    </tr>
                    <tr>
                        <td width="25%">请求报文</td>
                        <td colspan="4">请求体： json字符串</td>
                    </tr>
                    <tr>
                        <td width="25%">响应报文</td>
                        <td colspan="4">响应体： json字符串</td>
                    </tr>
                </table>
                <br>
                <br>

                <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                    <tr class="bg">
                        <td width="26%">参数名</td>
                        <td width="15%">数据类型</td>
                        <td width="15%">参数类型</td>
                        <td width="15%">是否必填</td>
                        <td width="29%">说明</td>
                    </tr>
                    <tr>
                        <td align="left">1.pageNum</td>
                        <td>Int</td>
                        <td>query</td>
                        <td>N</td>
                        <td>页码，默认1</td>
                    </tr>
                    <tr>
                        <td align="left">2.pageSize</td>
                        <td>Int</td>
                        <td>query</td>
                        <td>N</td>
                        <td>页大小，默认10</td>
                    </tr>
                    <tr>
                        <td align="left">3.status</td>
                        <td>String</td>
                        <td>query</td>
                        <td>N</td>
                        <td>报名状态：10-已报名，20-已取消</td>
                    </tr>
                    <tr class="specialHeight">
                        <td class="bg">示例</td>
                        <td colspan="4"><pre>{
  "pageNum": 1,
  "pageSize": 10,
  "status": "10"
}</pre></td>
                    </tr>
                </table>
                <br>
                <br>

                <h5 class="second_title">输出</h5>
                <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                    <tr class="bg">
                        <td>返回属性名</td>
                        <td colspan="2">类型</td>
                        <td colspan="2">说明</td>
                    </tr>
                    <tr>
                        <td align="left">1.resultCode</td>
                        <td colspan="2">String</td>
                        <td colspan="2">业务响应码</td>
                    </tr>
                    <tr>
                        <td align="left">2.resultMsg</td>
                        <td colspan="2">String</td>
                        <td colspan="2">业务响应信息</td>
                    </tr>
                    <tr>
                        <td align="left">3.data</td>
                        <td colspan="2">Object</td>
                        <td colspan="2">分页数据</td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left:20px">3.1.total</td>
                        <td colspan="2">Long</td>
                        <td colspan="2">总记录数</td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left:20px">3.2.records</td>
                        <td colspan="2">Array</td>
                        <td colspan="2">我的报名列表</td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left:30px">3.2.1.id</td>
                        <td colspan="2">Int</td>
                        <td colspan="2">主键ID</td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left:30px">3.2.2.activityId</td>
                        <td colspan="2">Int</td>
                        <td colspan="2">活动ID</td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left:30px">3.2.3.activityName</td>
                        <td colspan="2">String</td>
                        <td colspan="2">活动名称</td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left:30px">3.2.4.categoryName</td>
                        <td colspan="2">String</td>
                        <td colspan="2">活动类别名称</td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left:30px">3.2.5.startTime</td>
                        <td colspan="2">String</td>
                        <td colspan="2">活动开始时间</td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left:30px">3.2.6.endTime</td>
                        <td colspan="2">String</td>
                        <td colspan="2">活动结束时间</td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left:30px">3.2.7.activityStatus</td>
                        <td colspan="2">String</td>
                        <td colspan="2">活动状态</td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left:30px">3.2.8.registrationData</td>
                        <td colspan="2">String</td>
                        <td colspan="2">报名数据JSON</td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left:30px">3.2.9.registrationTime</td>
                        <td colspan="2">String</td>
                        <td colspan="2">报名时间</td>
                    </tr>
                    <tr>
                        <td align="left" style="padding-left:30px">3.2.10.status</td>
                        <td colspan="2">String</td>
                        <td colspan="2">报名状态</td>
                    </tr>
                    <tr class="specialHeight">
                        <td class="bg">示例</td>
                        <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "activityId": 1,
        "activityName": "春季摄影大赛",
        "categoryName": "摄影比赛",
        "startTime": "2024-03-01 09:00:00",
        "endTime": "2024-03-31 18:00:00",
        "activityStatus": "20",
        "registrationData": "{\"姓名\":\"张三\",\"作品图片\":\"http://minio.example.com/photo1.jpg\"}",
        "registrationTime": "2024-02-15 14:30:00",
        "status": "10"
      }
    ]
  }
}</pre></td>
                    </tr>
                </table>
                <br>
                <br>
            </div>
        </div>

        <p style="text-align: center; margin-top: 50px; font-style: italic; color: #666;">
            注：本文档为活动管理系统接口设计文档补充接口部分。<br>
            包含管理后台缺失的删除接口和小程序端缺失的取消报名、查询我的报名接口。<br>
            字体设置：正文宋体五号，标题宋体小四，完全符合Word文档标准。<br>
            表格格式已优化，复制到Word时将完美保持格式。
        </p>
    </div>
</body>
</html>
