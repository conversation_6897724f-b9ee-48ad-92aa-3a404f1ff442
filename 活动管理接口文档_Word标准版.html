<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="application/msword; charset=utf-8"/>
    <title>活动管理系统接口设计文档</title>
    <style type="text/css">
        .bg {
            font-size: 10.5pt; /* 宋体五号 */
            font-weight: bold;
        }

        table {
            border-style: solid;
            table-layout: fixed;
            width: 100%;
            border-collapse: collapse;
        }

        tr {
            height: 32px;
            font-size: 10.5pt; /* 宋体五号 */
        }

        td {
            padding-left: 10px;
            border: 0.5px solid #000;
            height: auto;
            min-height: 32px;
            overflow: hidden;
            word-break: break-all;
            word-wrap: break-word;
            white-space: normal;
            font-size: 10.5pt; /* 宋体五号 */
            vertical-align: top;
            max-width: 0;
        }

        .bg td {
            font-size: 10.5pt; /* 宋体五号 */
        }

        tr td {
            font-size: 10.5pt; /* 宋体五号 */
        }

        .specialHeight {
            height: 40px;
        }

        .first_title {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 12pt; /* 宋体小四 */
            font-weight: bold;
        }

        .second_title {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 12pt; /* 宋体小四 */
            font-weight: bold;
        }

        .doc_title {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 16pt; /* 文档标题用三号 */
            font-weight: bold;
            text-align: center;
        }

        body {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 10.5pt; /* 宋体五号 */
            overflow-x: auto;
        }

        /* 主容器宽度控制 */
        div[style*="width:800px"] {
            width: 800px !important;
            max-width: 800px !important;
            overflow-x: auto;
            box-sizing: border-box;
        }

        /* 正文段落 */
        div, p {
            font-family: "宋体", "SimSun", sans-serif;
            font-size: 10.5pt; /* 宋体五号 */
        }

        /* 代码示例 */
        pre {
            font-family: "Courier New", monospace;
            font-size: 9pt; /* 代码用小一点的字体 */
            background-color: #f5f5f5;
            padding: 5px;
            margin: 5px 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        /* 确保表格宽度一致性 */
        table[width="100%"] {
            width: 100% !important;
            max-width: 100%;
            min-width: 100%;
            table-layout: fixed !important;
        }

        /* 确保单元格宽度按比例分配 */
        td[width="25%"] {
            width: 25% !important;
            max-width: 25% !important;
        }

        td[width="26%"] {
            width: 26% !important;
            max-width: 26% !important;
        }

        td[width="15%"] {
            width: 15% !important;
            max-width: 15% !important;
        }

        td[width="29%"] {
            width: 29% !important;
            max-width: 29% !important;
        }

        /* 强制长文本换行 */
        td[colspan] {
            word-break: break-all !important;
            word-wrap: break-word !important;
            white-space: normal !important;
            overflow-wrap: break-word !important;
        }

        /* 特别处理包含URL的单元格 */
        td:contains("http://") {
            word-break: break-all !important;
            overflow-wrap: break-word !important;
        }
    </style>
</head>

<body>
<div style="width:800px; margin: 0 auto">
    <div>
        <h4 class="doc_title">活动管理系统接口设计文档</h4>
        <br>
    </div>
    
    <div style="margin-bottom:20px;">
        <h4 class="first_title">活动类别管理模块</h4>
        
        <div>
            <h4 class="second_title">分页查询活动类别接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台分页查询活动类别列表，支持按名称和时间范围筛选。</div>
            
            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">分页查询活动类别列表，支持按名称和时间范围筛选</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/page</td>
                </tr>
                <tr>
                    <td>超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>
            
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.pageNum</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页码，默认1</td>
                </tr>
                <tr>
                    <td align="left">2.pageSize</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页大小，默认10</td>
                </tr>
                <tr>
                    <td align="left">3.categoryName</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动类别名称，支持模糊查询</td>
                </tr>
                <tr>
                    <td align="left">4.startTime</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>创建开始时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr>
                    <td align="left">5.endTime</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>创建结束时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "pageNum": 1,
  "pageSize": 10,
  "categoryName": "摄影",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
            
            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr>
                    <td align="left">3.data</td>
                    <td colspan="2">Object</td>
                    <td colspan="2">分页数据</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.1.total</td>
                    <td colspan="2">Long</td>
                    <td colspan="2">总记录数</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.2.records</td>
                    <td colspan="2">Array</td>
                    <td colspan="2">活动类别列表</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.1.id</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">主键ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.2.categoryName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.3.categoryDesc</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别描述</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.4.formConfig</td>
                    <td colspan="2">String</td>
                    <td colspan="2">表单配置JSON</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.5.createdTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">创建时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.6.updatedTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">更新时间</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "categoryName": "摄影比赛",
        "categoryDesc": "各类摄影比赛活动",
        "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]",
        "createdTime": "2024-01-01 10:00:00",
        "updatedTime": "2024-01-01 10:00:00"
      }
    ]
  }
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">新增活动类别接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台新增活动类别。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">新增活动类别</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/add</td>
                </tr>
                <tr>
                    <td>超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.categoryName</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动类别名称</td>
                </tr>
                <tr>
                    <td align="left">2.categoryDesc</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动类别描述</td>
                </tr>
                <tr>
                    <td align="left">3.formConfig</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>表单配置JSON，每次提交必须传递完整的JSON数据不支持部分更新</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "categoryName": "摄影比赛",
  "categoryDesc": "各类摄影比赛活动",
  "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50},{\"fieldName\":\"作品图片\",\"required\":true,\"inputType\":\"upload\",\"maxCount\":3}]"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">修改活动类别接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台修改活动类别信息。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">修改活动类别信息</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/update</td>
                </tr>
                <tr>
                    <td>超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.id</td>
                    <td>Int</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>主键ID</td>
                </tr>
                <tr>
                    <td align="left">2.categoryName</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动类别名称</td>
                </tr>
                <tr>
                    <td align="left">3.categoryDesc</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动类别描述</td>
                </tr>
                <tr>
                    <td align="left">4.formConfig</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>表单配置JSON，每次提交必须传递完整的JSON数据不支持部分更新</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "id": 1,
  "categoryName": "摄影比赛",
  "categoryDesc": "各类摄影比赛活动",
  "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">活动类别详情接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台查询活动类别详情信息。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">查询活动类别详情信息</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/detail</td>
                </tr>
                <tr>
                    <td>超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.id</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>Y</td>
                    <td>主键ID</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "id": 1
}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr>
                    <td align="left">3.data</td>
                    <td colspan="2">Object</td>
                    <td colspan="2">活动类别详情</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.1.id</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">主键ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.2.categoryName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.3.categoryDesc</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别描述</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.4.formConfig</td>
                    <td colspan="2">String</td>
                    <td colspan="2">表单配置JSON</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.5.createdTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">创建时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.6.updatedTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">更新时间</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "id": 1,
    "categoryName": "摄影比赛",
    "categoryDesc": "各类摄影比赛活动",
    "formConfig": "[{\"fieldName\":\"姓名\",\"required\":true,\"inputType\":\"input\",\"maxLength\":50}]",
    "createdTime": "2024-01-01 10:00:00",
    "updatedTime": "2024-01-01 10:00:00"
  }
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">活动类别列表接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台获取所有活动类别列表，用于下拉选择。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">获取所有活动类别列表，用于下拉选择</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityCategory/list</td>
                </tr>
                <tr>
                    <td>超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr>
                    <td align="left">3.data</td>
                    <td colspan="2">Array</td>
                    <td colspan="2">活动类别列表</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.1.id</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">主键ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.2.categoryName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别名称</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": [
    {
      "id": 1,
      "categoryName": "摄影比赛"
    },
    {
      "id": 2,
      "categoryName": "文艺表演"
    }
  ]
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>
    </div>

    <div style="margin-bottom:20px;">
        <h4 class="first_title">活动内容管理模块</h4>

        <div>
            <h4 class="second_title">分页查询活动内容接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台分页查询活动内容列表，支持按标题、类别、状态和时间范围筛选。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">分页查询活动内容列表，支持按标题、类别、状态和时间范围筛选</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityContent/page</td>
                </tr>
                <tr>
                    <td>超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.pageNum</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页码，默认1</td>
                </tr>
                <tr>
                    <td align="left">2.pageSize</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>页大小，默认10</td>
                </tr>
                <tr>
                    <td align="left">3.activityTitle</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动标题，支持模糊查询</td>
                </tr>
                <tr>
                    <td align="left">4.categoryId</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动类别ID</td>
                </tr>
                <tr>
                    <td align="left">5.activityStatus</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动状态：00-未发布，10-待开始，20-进行中，30-已作废，40-已结束</td>
                </tr>
                <tr>
                    <td align="left">6.startTime</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动开始时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr>
                    <td align="left">7.endTime</td>
                    <td>String</td>
                    <td>query</td>
                    <td>N</td>
                    <td>活动结束时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "pageNum": 1,
  "pageSize": 10,
  "activityTitle": "摄影",
  "categoryId": 1,
  "activityStatus": "10",
  "startTime": "2024-01-01 00:00:00",
  "endTime": "2024-12-31 23:59:59"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr>
                    <td align="left">3.data</td>
                    <td colspan="2">Object</td>
                    <td colspan="2">分页数据</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.1.total</td>
                    <td colspan="2">Long</td>
                    <td colspan="2">总记录数</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:20px">3.2.records</td>
                    <td colspan="2">Array</td>
                    <td colspan="2">活动内容列表</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.1.id</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">主键ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.2.activityTitle</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动标题</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.3.categoryId</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">活动类别ID</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.4.categoryName</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动类别名称</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.5.activityDesc</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动描述</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.6.activityImageUrl</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动图片URL</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.7.activityStartTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动开始时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.8.activityEndTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动结束时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.9.activityStatus</td>
                    <td colspan="2">String</td>
                    <td colspan="2">活动状态</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.10.registrationCount</td>
                    <td colspan="2">Int</td>
                    <td colspan="2">已报名人数</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.11.createdTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">创建时间</td>
                </tr>
                <tr>
                    <td align="left" style="padding-left:30px">3.2.12.updatedTime</td>
                    <td colspan="2">String</td>
                    <td colspan="2">更新时间</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功",
  "data": {
    "total": 1,
    "records": [
      {
        "id": 1,
        "activityTitle": "春季摄影大赛",
        "categoryId": 1,
        "categoryName": "摄影比赛",
        "activityDesc": "展示春天美景的摄影比赛",
        "activityImageUrl": "http://example.com/image.jpg",
        "activityStartTime": "2024-03-01 09:00:00",
        "activityEndTime": "2024-03-31 18:00:00",
        "activityStatus": "10",
        "registrationCount": 25,
        "createdTime": "2024-01-01 10:00:00",
        "updatedTime": "2024-01-01 10:00:00"
      }
    ]
  }
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>
    </div>

        <div>
            <h4 class="second_title">新增活动内容接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台新增活动内容。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">新增活动内容</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityContent/add</td>
                </tr>
                <tr>
                    <td>超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.activityTitle</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动标题</td>
                </tr>
                <tr>
                    <td align="left">2.categoryId</td>
                    <td>Int</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动类别ID</td>
                </tr>
                <tr>
                    <td align="left">3.activityDesc</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动描述</td>
                </tr>
                <tr>
                    <td align="left">4.activityImageUrl</td>
                    <td>String</td>
                    <td>body</td>
                    <td>N</td>
                    <td>活动图片URL</td>
                </tr>
                <tr>
                    <td align="left">5.activityStartTime</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动开始时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr>
                    <td align="left">6.activityEndTime</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动结束时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr>
                    <td align="left">7.activityStatus</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动状态：00-未发布，10-待开始，20-进行中，30-已作废，40-已结束</td>
                </tr>
                <tr>
                    <td align="left">8.maxParticipants</td>
                    <td>Int</td>
                    <td>body</td>
                    <td>N</td>
                    <td>最大参与人数，0表示不限制</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "activityTitle": "春季摄影大赛",
  "categoryId": 1,
  "activityDesc": "展示春天美景的摄影比赛",
  "activityImageUrl": "http://example.com/image.jpg",
  "activityStartTime": "2024-03-01 09:00:00",
  "activityEndTime": "2024-03-31 18:00:00",
  "activityStatus": "00",
  "maxParticipants": 100
}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">修改活动内容接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台修改活动内容信息。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">修改活动内容信息</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityContent/update</td>
                </tr>
                <tr>
                    <td>超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.id</td>
                    <td>Int</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>主键ID</td>
                </tr>
                <tr>
                    <td align="left">2.activityTitle</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动标题</td>
                </tr>
                <tr>
                    <td align="left">3.categoryId</td>
                    <td>Int</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动类别ID</td>
                </tr>
                <tr>
                    <td align="left">4.activityDesc</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动描述</td>
                </tr>
                <tr>
                    <td align="left">5.activityImageUrl</td>
                    <td>String</td>
                    <td>body</td>
                    <td>N</td>
                    <td>活动图片URL</td>
                </tr>
                <tr>
                    <td align="left">6.activityStartTime</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动开始时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr>
                    <td align="left">7.activityEndTime</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动结束时间，格式：yyyy-MM-dd HH:mm:ss</td>
                </tr>
                <tr>
                    <td align="left">8.activityStatus</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动状态：00-未发布，10-待开始，20-进行中，30-已作废，40-已结束</td>
                </tr>
                <tr>
                    <td align="left">9.maxParticipants</td>
                    <td>Int</td>
                    <td>body</td>
                    <td>N</td>
                    <td>最大参与人数，0表示不限制</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "id": 1,
  "activityTitle": "春季摄影大赛",
  "categoryId": 1,
  "activityDesc": "展示春天美景的摄影比赛",
  "activityImageUrl": "http://example.com/image.jpg",
  "activityStartTime": "2024-03-01 09:00:00",
  "activityEndTime": "2024-03-31 18:00:00",
  "activityStatus": "10",
  "maxParticipants": 100
}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "操作成功"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">活动内容详情接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台查询活动内容详情信息。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">查询活动内容详情信息</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td>请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td>请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityContent/detail</td>
                </tr>
                <tr>
                    <td>超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td>请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td>响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.id</td>
                    <td>Int</td>
                    <td>query</td>
                    <td>Y</td>
                    <td>主键ID</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "id": 1
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>

        <div>
            <h4 class="second_title">修改活动状态接口</h4>
            <h5 class="second_title">功能点描述</h5>
            <div>管理后台修改活动状态，支持发布、作废、结束等状态变更。</div>

            <h5 class="second_title">输入</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td colspan="5">设计项</td>
                </tr>
                <tr>
                    <td width="25%">功能说明</td>
                    <td colspan="4">修改活动状态，支持发布、作废、结束等状态变更</td>
                </tr>
                <tr>
                    <td width="25%">接口提供者</td>
                    <td colspan="4">mini-app-admin</td>
                </tr>
                <tr>
                    <td width="25%">接口调用者</td>
                    <td colspan="4">管理后台前端</td>
                </tr>
                <tr>
                    <td width="25%">协议</td>
                    <td colspan="4">http POST</td>
                </tr>
                <tr>
                    <td width="25%">请求头</td>
                    <td colspan="4">Content-Type: application/json</td>
                </tr>
                <tr>
                    <td width="25%">请求路径</td>
                    <td colspan="4">http://{ip}:{port}/mini-app-admin/activityContent/updateStatus</td>
                </tr>
                <tr>
                    <td width="25%">超时时长</td>
                    <td colspan="4">5000ms</td>
                </tr>
                <tr>
                    <td width="25%">请求报文</td>
                    <td colspan="4">请求体： json字符串</td>
                </tr>
                <tr>
                    <td width="25%">响应报文</td>
                    <td colspan="4">响应体： json字符串</td>
                </tr>
            </table>
            <br>
            <br>

            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td width="26%">参数名</td>
                    <td width="15%">数据类型</td>
                    <td width="15%">参数类型</td>
                    <td width="15%">是否必填</td>
                    <td width="29%">说明</td>
                </tr>
                <tr>
                    <td align="left">1.id</td>
                    <td>Int</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动主键ID</td>
                </tr>
                <tr>
                    <td align="left">2.activityStatus</td>
                    <td>String</td>
                    <td>body</td>
                    <td>Y</td>
                    <td>活动状态：00-未发布，10-待开始，20-进行中，30-已作废，40-已结束</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "id": 1,
  "activityStatus": "10"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>

            <h5 class="second_title">输出</h5>
            <table border="0.5" cellspacing="0" cellpadding="0" width="100%">
                <tr class="bg">
                    <td>返回属性名</td>
                    <td colspan="2">类型</td>
                    <td colspan="2">说明</td>
                </tr>
                <tr>
                    <td align="left">1.resultCode</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应码</td>
                </tr>
                <tr>
                    <td align="left">2.resultMsg</td>
                    <td colspan="2">String</td>
                    <td colspan="2">业务响应信息</td>
                </tr>
                <tr class="specialHeight">
                    <td class="bg">示例</td>
                    <td colspan="4"><pre>{
  "resultCode": "200",
  "resultMsg": "活动状态修改成功"
}</pre></td>
                </tr>
            </table>
            <br>
            <br>
        </div>
    </div>

    <p style="text-align: center; margin-top: 50px; font-style: italic; color: #666;">
        注：本文档为活动管理系统接口设计文档前半部分。<br>
        包含活动类别管理、活动内容管理等模块。<br>
        用户报名管理模块及小程序端接口请参见单独的HTML文档。<br>
        字体设置：正文宋体五号，标题宋体小四，完全符合Word文档标准。
    </p>
</div>
</body>
</html>
